import 'package:flutter/material.dart';

class ContactsTabScreen extends StatefulWidget {
  const ContactsTabScreen({super.key});

  @override
  State<ContactsTabScreen> createState() => _ContactsTabScreenState();
}

class _ContactsTabScreenState extends State<ContactsTabScreen> {
  int selectedFilterIndex = 0; // 0 for "6 friends", 1 for "Suggestions"

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          // Status bar space
          Container(
            height: MediaQuery.of(context).padding.top,
            color: Colors.white,
          ),

          // Search bar at top
          Container(
            margin: const EdgeInsets.fromLTRB(16, 16, 16, 6),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: const Color(0xFFF6F6F6),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.search,
                  color: Color(0xFF777777),
                  size: 18,
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Username',
                      hintStyle: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF777777),
                        fontFamily: 'IBM Plex Sans',
                      ),
                      border: InputBorder.none,
                      isDense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ),
                Container(
                  width: 1,
                  height: 24,
                  color: const Color(0xFFE5E5E5),
                ),
                const SizedBox(width: 12),
                const Icon(
                  Icons.qr_code_scanner,
                  color: Color(0xFF777777),
                  size: 22,
                ),
              ],
            ),
          ),

          // Friend requests section
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE0B83E),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Stack(
                    children: [
                      const Icon(
                        Icons.person_add,
                        color: Colors.white,
                        size: 12,
                      ),
                      Positioned(
                        right: -2,
                        top: -2,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Color(0xFFD33636),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Friend requests (20)',
                  style: TextStyle(
                    fontSize: 16,
                    fontFamily: 'IBM Plex Sans',
                    color: Color(0xFF292929),
                  ),
                ),
              ],
            ),
          ),

          // Divider
          Container(
            height: 4,
            color: const Color(0xFFF6F6F6),
          ),

          // Filter tabs
          Container(
            margin: const EdgeInsets.fromLTRB(16, 12, 16, 0),
            child: Row(
              children: [
                _buildFilterTab('6 friends', 0),
                const SizedBox(width: 12),
                _buildFilterTab('Suggestions', 1),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Contacts list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _mockContacts.length,
              itemBuilder: (context, index) {
                final contact = _mockContacts[index];
                return _buildContactItem(contact);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTab(String text, int index) {
    final isSelected = selectedFilterIndex == index;
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedFilterIndex = index;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0x1A6C4EFF) : const Color(0xFFF6F6F6),
          borderRadius: BorderRadius.circular(27),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            fontFamily: 'IBM Plex Sans',
            color:
                isSelected ? const Color(0xFF6C4EFF) : const Color(0xFF777777),
          ),
        ),
      ),
    );
  }

  Widget _buildContactItem(MockContact contact) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Avatar with indicator
          Stack(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  image: contact.avatarImage != null
                      ? DecorationImage(
                          image: AssetImage(contact.avatarImage!),
                          fit: BoxFit.cover,
                        )
                      : null,
                  color:
                      contact.avatarImage == null ? contact.avatarColor : null,
                ),
                child: contact.avatarImage == null
                    ? Center(
                        child: Text(
                          contact.initials,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      )
                    : null,
              ),
              if (contact.isOnline)
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(width: 12),

          // Contact info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  contact.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'IBM Plex Sans',
                    color: Color(0xFF292929),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  contact.status,
                  style: const TextStyle(
                    fontSize: 16,
                    fontFamily: 'IBM Plex Sans',
                    color: Color(0xFF777777),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Mock data for demonstration
class MockContact {
  final String name;
  final String status;
  final bool isOnline;
  final Color avatarColor;
  final String initials;
  final String? avatarImage;

  MockContact({
    required this.name,
    required this.status,
    required this.isOnline,
    required this.avatarColor,
    required this.initials,
    this.avatarImage,
  });
}

final List<MockContact> _mockContacts = [
  MockContact(
    name: 'Annette Black',
    status: 'Username',
    isOnline: true,
    avatarColor: const Color(0xFF6C4EFF),
    initials: 'AB',
  ),
  MockContact(
    name: 'Annette Black',
    status: 'Username',
    isOnline: true,
    avatarColor: const Color(0xFF4CAF50),
    initials: 'AB',
  ),
  MockContact(
    name: 'Annette Black',
    status: 'Username',
    isOnline: false,
    avatarColor: const Color(0xFFFF9800),
    initials: 'AB',
  ),
  MockContact(
    name: 'Annette Black',
    status: 'Username',
    isOnline: true,
    avatarColor: const Color(0xFFE91E63),
    initials: 'AB',
  ),
  MockContact(
    name: 'Annette Black',
    status: 'Username',
    isOnline: false,
    avatarColor: const Color(0xFF9C27B0),
    initials: 'AB',
  ),
  MockContact(
    name: 'Annette Black',
    status: 'Username',
    isOnline: true,
    avatarColor: const Color(0xFF00BCD4),
    initials: 'AB',
  ),
];
