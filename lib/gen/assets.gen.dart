// dart format width=80

/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsAbiGen {
  const $AssetsAbiGen();

  /// File path: assets/abi/erc20tokenabi.json
  String get erc20tokenabi => 'assets/abi/erc20tokenabi.json';

  /// File path: assets/abi/usdt.abis.json
  String get usdtAbis => 'assets/abi/usdt.abis.json';

  /// List of all assets
  List<String> get values => [erc20tokenabi, usdtAbis];
}

class $AssetsDefibrowserGen {
  const $AssetsDefibrowserGen();

  /// File path: assets/defibrowser/posi.min.js
  String get posiMin => 'assets/defibrowser/posi.min.js';

  /// List of all assets
  List<String> get values => [posiMin];
}

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/ic_chat.svg
  SvgGenImage get icChat => const SvgGenImage('assets/icons/ic_chat.svg');

  /// File path: assets/icons/ic_contacts.svg
  SvgGenImage get icContacts =>
      const SvgGenImage('assets/icons/ic_contacts.svg');

  /// File path: assets/icons/ic_contacts_body.svg
  SvgGenImage get icContactsBody =>
      const SvgGenImage('assets/icons/ic_contacts_body.svg');

  /// File path: assets/icons/ic_contacts_head.svg
  SvgGenImage get icContactsHead =>
      const SvgGenImage('assets/icons/ic_contacts_head.svg');

  /// File path: assets/icons/ic_find_nearby.svg
  SvgGenImage get icFindNearby =>
      const SvgGenImage('assets/icons/ic_find_nearby.svg');

  /// File path: assets/icons/ic_nav_chat.svg
  SvgGenImage get icNavChat =>
      const SvgGenImage('assets/icons/ic_nav_chat.svg');

  /// File path: assets/icons/ic_nav_contacts.svg
  SvgGenImage get icNavContacts =>
      const SvgGenImage('assets/icons/ic_nav_contacts.svg');

  /// File path: assets/icons/ic_nav_people_active.svg
  SvgGenImage get icNavPeopleActive =>
      const SvgGenImage('assets/icons/ic_nav_people_active.svg');

  /// File path: assets/icons/ic_nav_settings.svg
  SvgGenImage get icNavSettings =>
      const SvgGenImage('assets/icons/ic_nav_settings.svg');

  /// File path: assets/icons/ic_qr_code.svg
  SvgGenImage get icQrCode => const SvgGenImage('assets/icons/ic_qr_code.svg');

  /// File path: assets/icons/ic_search.svg
  SvgGenImage get icSearch => const SvgGenImage('assets/icons/ic_search.svg');

  /// File path: assets/icons/ic_search_circle.svg
  SvgGenImage get icSearchCircle =>
      const SvgGenImage('assets/icons/ic_search_circle.svg');

  /// File path: assets/icons/ic_search_dot.svg
  SvgGenImage get icSearchDot =>
      const SvgGenImage('assets/icons/ic_search_dot.svg');

  /// File path: assets/icons/ic_settings.svg
  SvgGenImage get icSettings =>
      const SvgGenImage('assets/icons/ic_settings.svg');

  /// File path: assets/icons/ic_settings_dot.svg
  SvgGenImage get icSettingsDot =>
      const SvgGenImage('assets/icons/ic_settings_dot.svg');

  /// File path: assets/icons/ic_settings_gear.svg
  SvgGenImage get icSettingsGear =>
      const SvgGenImage('assets/icons/ic_settings_gear.svg');

  /// File path: assets/icons/ic_share_link.svg
  SvgGenImage get icShareLink =>
      const SvgGenImage('assets/icons/ic_share_link.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
        icChat,
        icContacts,
        icContactsBody,
        icContactsHead,
        icFindNearby,
        icNavChat,
        icNavContacts,
        icNavPeopleActive,
        icNavSettings,
        icQrCode,
        icSearch,
        icSearchCircle,
        icSearchDot,
        icSettings,
        icSettingsDot,
        icSettingsGear,
        icShareLink
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/avatar_albert_flores.png
  AssetGenImage get avatarAlbertFlores =>
      const AssetGenImage('assets/images/avatar_albert_flores.png');

  /// File path: assets/images/avatar_kristin_watson.png
  AssetGenImage get avatarKristinWatson =>
      const AssetGenImage('assets/images/avatar_kristin_watson.png');

  /// File path: assets/images/avatar_sample.png
  AssetGenImage get avatarSample =>
      const AssetGenImage('assets/images/avatar_sample.png');

  /// File path: assets/images/avatar_theresa_webb.png
  AssetGenImage get avatarTheresaWebb =>
      const AssetGenImage('assets/images/avatar_theresa_webb.png');

  /// File path: assets/images/bg_blur.png
  AssetGenImage get bgBlur => const AssetGenImage('assets/images/bg_blur.png');

  /// File path: assets/images/bg_home.png
  AssetGenImage get bgHome => const AssetGenImage('assets/images/bg_home.png');

  /// File path: assets/images/bg_loading_login.png
  AssetGenImage get bgLoadingLogin =>
      const AssetGenImage('assets/images/bg_loading_login.png');

  /// File path: assets/images/bg_login.png
  AssetGenImage get bgLogin =>
      const AssetGenImage('assets/images/bg_login.png');

  /// File path: assets/images/bg_wallet.svg
  SvgGenImage get bgWallet => const SvgGenImage('assets/images/bg_wallet.svg');

  /// File path: assets/images/bg_wallet1.png
  AssetGenImage get bgWallet1 =>
      const AssetGenImage('assets/images/bg_wallet1.png');

  /// File path: assets/images/default_background.png
  AssetGenImage get defaultBackground =>
      const AssetGenImage('assets/images/default_background.png');

  /// File path: assets/images/home_sample1.png
  AssetGenImage get homeSample1 =>
      const AssetGenImage('assets/images/home_sample1.png');

  /// File path: assets/images/image_1.png
  AssetGenImage get image1 => const AssetGenImage('assets/images/image_1.png');

  /// File path: assets/images/image_2.png
  AssetGenImage get image2 => const AssetGenImage('assets/images/image_2.png');

  /// File path: assets/images/image_3.png
  AssetGenImage get image3 => const AssetGenImage('assets/images/image_3.png');

  /// File path: assets/images/img_gao_term.png
  AssetGenImage get imgGaoTerm =>
      const AssetGenImage('assets/images/img_gao_term.png');

  /// File path: assets/images/img_runx.png
  AssetGenImage get imgRunx =>
      const AssetGenImage('assets/images/img_runx.png');

  /// File path: assets/images/img_toii.png
  AssetGenImage get imgToii =>
      const AssetGenImage('assets/images/img_toii.png');

  /// File path: assets/images/img_travel.png
  AssetGenImage get imgTravel =>
      const AssetGenImage('assets/images/img_travel.png');

  /// File path: assets/images/logo.png
  AssetGenImage get logo => const AssetGenImage('assets/images/logo.png');

  /// File path: assets/images/nft_sample.png
  AssetGenImage get nftSample =>
      const AssetGenImage('assets/images/nft_sample.png');

  /// File path: assets/images/onboarding_1.png
  AssetGenImage get onboarding1 =>
      const AssetGenImage('assets/images/onboarding_1.png');

  /// File path: assets/images/onboarding_2.png
  AssetGenImage get onboarding2 =>
      const AssetGenImage('assets/images/onboarding_2.png');

  /// File path: assets/images/onboarding_3.png
  AssetGenImage get onboarding3 =>
      const AssetGenImage('assets/images/onboarding_3.png');

  /// File path: assets/images/welcome.png
  AssetGenImage get welcome => const AssetGenImage('assets/images/welcome.png');

  /// List of all assets
  List<dynamic> get values => [
        avatarAlbertFlores,
        avatarKristinWatson,
        avatarSample,
        avatarTheresaWebb,
        bgBlur,
        bgHome,
        bgLoadingLogin,
        bgLogin,
        bgWallet,
        bgWallet1,
        defaultBackground,
        homeSample1,
        image1,
        image2,
        image3,
        imgGaoTerm,
        imgRunx,
        imgToii,
        imgTravel,
        logo,
        nftSample,
        onboarding1,
        onboarding2,
        onboarding3,
        welcome
      ];
}

class $AssetsJsonGen {
  const $AssetsJsonGen();

  /// File path: assets/json/chainIcon.json
  String get chainIcon => 'assets/json/chainIcon.json';

  /// File path: assets/json/chainlist.json
  String get chainlist => 'assets/json/chainlist.json';

  /// File path: assets/json/defaultTokens.json
  String get defaultTokens => 'assets/json/defaultTokens.json';

  /// File path: assets/json/login.lottie
  String get login => 'assets/json/login.lottie';

  /// File path: assets/json/processing.json
  String get processing => 'assets/json/processing.json';

  /// File path: assets/json/topchain.json
  String get topchain => 'assets/json/topchain.json';

  /// List of all assets
  List<String> get values =>
      [chainIcon, chainlist, defaultTokens, login, processing, topchain];
}

class Assets {
  const Assets._();

  static const $AssetsAbiGen abi = $AssetsAbiGen();
  static const $AssetsDefibrowserGen defibrowser = $AssetsDefibrowserGen();
  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsJsonGen json = $AssetsJsonGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
    this.animation,
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;
  final AssetGenImageAnimation? animation;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class AssetGenImageAnimation {
  const AssetGenImageAnimation({
    required this.isAnimation,
    required this.duration,
    required this.frames,
  });

  final bool isAnimation;
  final Duration duration;
  final int frames;
}

class SvgGenImage {
  const SvgGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = false;

  const SvgGenImage.vec(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    _svg.ColorMapper? colorMapper,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
        colorMapper: colorMapper,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
